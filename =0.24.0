Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Requirement already satisfied: openai==1.3.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (1.3.0)
Requirement already satisfied: python-dotenv==1.0.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (1.0.0)
Requirement already satisfied: httpx in /home/<USER>/anaconda3/lib/python3.11/site-packages (0.28.1)
Collecting paho-mqtt==1.6.1
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/f8/dd/4b75dcba025f8647bc9862ac17299e0d7d12d3beadbf026d8c8d74215c12/paho-mqtt-1.6.1.tar.gz (99 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: anyio<4,>=3.5.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from openai==1.3.0) (3.7.1)
Requirement already satisfied: distro<2,>=1.7.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from openai==1.3.0) (1.8.0)
Requirement already satisfied: pydantic<3,>=1.9.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from openai==1.3.0) (2.11.7)
Requirement already satisfied: tqdm>4 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from openai==1.3.0) (4.65.0)
Requirement already satisfied: typing-extensions<5,>=4.5 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from openai==1.3.0) (4.14.1)
Requirement already satisfied: certifi in /home/<USER>/anaconda3/lib/python3.11/site-packages (from httpx) (2024.2.2)
Requirement already satisfied: httpcore==1.* in /home/<USER>/anaconda3/lib/python3.11/site-packages (from httpx) (1.0.9)
Requirement already satisfied: idna in /home/<USER>/anaconda3/lib/python3.11/site-packages (from httpx) (3.4)
Requirement already satisfied: sniffio>=1.1 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from anyio<4,>=3.5.0->openai==1.3.0) (1.3.0)
Requirement already satisfied: h11>=0.16 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from httpcore==1.*->httpx) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from pydantic<3,>=1.9.0->openai==1.3.0) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from pydantic<3,>=1.9.0->openai==1.3.0) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from pydantic<3,>=1.9.0->openai==1.3.0) (0.4.1)
Building wheels for collected packages: paho-mqtt
  Building wheel for paho-mqtt (setup.py): started
  Building wheel for paho-mqtt (setup.py): finished with status 'done'
  Created wheel for paho-mqtt: filename=paho_mqtt-1.6.1-py3-none-any.whl size=62118 sha256=24b5942628365eef4210ad9b29340427979d70427cc8bf5add05c2180c1f5625
  Stored in directory: /home/<USER>/.cache/pip/wheels/ab/2d/89/e6591f047677d24b8cab3938bfa3ea5a2f40eab67fb7fe16b2
Successfully built paho-mqtt
Installing collected packages: paho-mqtt
Successfully installed paho-mqtt-1.6.1
